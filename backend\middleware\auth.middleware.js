import AppError from "../utils/error.utils.js";
import jwt from "jsonwebtoken";
import userModel from '../models/user.model.js';


const isLoggedIn = async (req, res, next) => {
    try {
        const { token } = req.cookies;

        if (!token) {
            return next(new AppError("Unauthenticated, please login again", 401))
        }

        if (!process.env.JWT_SECRET) {
            console.error('JWT_SECRET is not defined');
            return next(new AppError("Server configuration error", 500));
        }

        const userDetails = await jwt.verify(token, process.env.JWT_SECRET);

        if (!userDetails || !userDetails.id) {
            return next(new AppError("Invalid token, please login again", 401));
        }

        req.user = userDetails;
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return next(new AppError("Invalid token, please login again", 401));
        } else if (error.name === 'TokenExpiredError') {
            return next(new AppError("Token expired, please login again", 401));
        } else {
            console.error('JWT verification error:', error);
            return next(new AppError("Authentication failed", 401));
        }
    }
}

// authorised roles
const authorisedRoles = (...roles) => async (req, res, next) => {
    const currentUserRoles = req.user.role;
    if (!roles.includes(currentUserRoles)) {
        return next(new AppError("You do not have permission to access this routes", 403))
    }
    next();
}

const authorizeSubscriber = async (req, res, next) => {
    try {
        const {role, id} = req.user;

        if (!id) {
            return next(new AppError('User ID not found in token', 401));
        }

        const user = await userModel.findById(id);

        if (!user) {
            return next(new AppError('User not found', 404));
        }

        const subscriptionStatus = user.subscription?.status;

        if (role !== 'ADMIN' && subscriptionStatus !== 'active') {
            return next(
                new AppError('Please subscribe to access this route!', 403)
            )
        }

        next();
    } catch (error) {
        console.error('Authorization error:', error);
        return next(new AppError('Authorization failed', 500));
    }
}

export {
    isLoggedIn,
    authorisedRoles,
    authorizeSubscriber
}