import fs from 'fs';
import path from 'path';

/**
 * Ensure directory exists, create if it doesn't
 * @param {string} dirPath - Directory path to ensure
 */
export const ensureDirectoryExists = (dirPath) => {
    try {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`📁 Created directory: ${dirPath}`);
        }
    } catch (error) {
        console.error(`❌ Failed to create directory ${dirPath}:`, error.message);
        throw error;
    }
};

/**
 * Safely delete a file
 * @param {string} filePath - File path to delete
 */
export const safeDeleteFile = async (filePath) => {
    try {
        if (fs.existsSync(filePath)) {
            await fs.promises.unlink(filePath);
            console.log(`🗑️ Deleted file: ${filePath}`);
        }
    } catch (error) {
        console.warn(`⚠️ Failed to delete file ${filePath}:`, error.message);
        // Don't throw error for cleanup failures
    }
};

/**
 * Check if file exists and is accessible
 * @param {string} filePath - File path to check
 * @returns {boolean} - True if file exists and is accessible
 */
export const isFileAccessible = async (filePath) => {
    try {
        await fs.promises.access(filePath, fs.constants.F_OK);
        return true;
    } catch {
        return false;
    }
};

/**
 * Get file size in bytes
 * @param {string} filePath - File path
 * @returns {number} - File size in bytes
 */
export const getFileSize = async (filePath) => {
    try {
        const stats = await fs.promises.stat(filePath);
        return stats.size;
    } catch (error) {
        console.error(`Failed to get file size for ${filePath}:`, error.message);
        return 0;
    }
};

/**
 * Validate file type based on extension
 * @param {string} filename - File name
 * @param {string[]} allowedExtensions - Array of allowed extensions (e.g., ['.jpg', '.png'])
 * @returns {boolean} - True if file type is allowed
 */
export const isValidFileType = (filename, allowedExtensions) => {
    const ext = path.extname(filename).toLowerCase();
    return allowedExtensions.includes(ext);
};

/**
 * Clean up old temporary files
 * @param {string} dirPath - Directory to clean
 * @param {number} maxAgeHours - Maximum age in hours (default: 24)
 */
export const cleanupOldFiles = async (dirPath, maxAgeHours = 24) => {
    try {
        if (!fs.existsSync(dirPath)) {
            return;
        }

        const files = await fs.promises.readdir(dirPath);
        const now = Date.now();
        const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds

        for (const file of files) {
            const filePath = path.join(dirPath, file);
            try {
                const stats = await fs.promises.stat(filePath);
                const age = now - stats.mtime.getTime();

                if (age > maxAge) {
                    await fs.promises.unlink(filePath);
                    console.log(`🧹 Cleaned up old file: ${filePath}`);
                }
            } catch (error) {
                console.warn(`Failed to process file ${filePath}:`, error.message);
            }
        }
    } catch (error) {
        console.error(`Failed to cleanup directory ${dirPath}:`, error.message);
    }
};

/**
 * Initialize required directories
 */
export const initializeDirectories = () => {
    const requiredDirs = [
        'uploads',
        'uploads/avatars',
        'uploads/courses',
        'uploads/blogs',
        'uploads/temp'
    ];

    requiredDirs.forEach(dir => {
        ensureDirectoryExists(dir);
    });

    // Schedule cleanup of temp files every hour
    setInterval(() => {
        cleanupOldFiles('uploads/temp', 1); // Clean files older than 1 hour
        cleanupOldFiles('uploads', 24); // Clean general uploads older than 24 hours
    }, 60 * 60 * 1000); // Run every hour

    console.log('✅ File system utilities initialized');
};
