import { body, validationResult } from 'express-validator';
import AppError from '../utils/error.utils.js';

// Middleware to handle validation errors
export const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        return next(new AppError(errorMessages.join(', '), 400));
    }
    next();
};

// User registration validation
export const validateUserRegistration = [
    body('fullName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Full name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('Full name can only contain letters and spaces'),
    
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    
    body('password')
        .isLength({ min: 6, max: 128 })
        .withMessage('Password must be between 6 and 128 characters')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    
    body('number')
        .isMobilePhone()
        .withMessage('Please provide a valid phone number'),
    
    handleValidationErrors
];

// User login validation
export const validateUserLogin = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    
    body('password')
        .notEmpty()
        .withMessage('Password is required'),
    
    handleValidationErrors
];

// Course creation validation
export const validateCourseCreation = [
    body('title')
        .trim()
        .isLength({ min: 3, max: 100 })
        .withMessage('Course title must be between 3 and 100 characters'),
    
    body('description')
        .trim()
        .isLength({ min: 10, max: 1000 })
        .withMessage('Course description must be between 10 and 1000 characters'),
    
    body('category')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Category must be between 2 and 50 characters'),
    
    body('createdBy')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Created by must be between 2 and 50 characters'),
    
    body('price')
        .optional()
        .isNumeric()
        .withMessage('Price must be a number')
        .isFloat({ min: 0 })
        .withMessage('Price must be a positive number'),
    
    handleValidationErrors
];

// Contact form validation
export const validateContactForm = [
    body('name')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('Name can only contain letters and spaces'),
    
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    
    body('message')
        .trim()
        .isLength({ min: 10, max: 1000 })
        .withMessage('Message must be between 10 and 1000 characters'),
    
    body('number')
        .optional()
        .isMobilePhone()
        .withMessage('Please provide a valid phone number'),
    
    handleValidationErrors
];

// Blog creation validation
export const validateBlogCreation = [
    body('title')
        .trim()
        .isLength({ min: 3, max: 200 })
        .withMessage('Blog title must be between 3 and 200 characters'),
    
    body('description')
        .trim()
        .isLength({ min: 10, max: 2000 })
        .withMessage('Blog description must be between 10 and 2000 characters'),
    
    body('link')
        .isURL()
        .withMessage('Please provide a valid URL'),
    
    handleValidationErrors
];

// Password change validation
export const validatePasswordChange = [
    body('oldPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    
    body('newPassword')
        .isLength({ min: 6, max: 128 })
        .withMessage('New password must be between 6 and 128 characters')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
    
    handleValidationErrors
];

// MongoDB ObjectId validation
export const validateObjectId = (paramName) => [
    body(paramName)
        .optional()
        .isMongoId()
        .withMessage(`${paramName} must be a valid MongoDB ObjectId`),
    
    handleValidationErrors
];

// Generic sanitization middleware
export const sanitizeInput = (req, res, next) => {
    // Remove any potential XSS attempts
    const sanitizeString = (str) => {
        if (typeof str !== 'string') return str;
        return str.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                  .replace(/javascript:/gi, '')
                  .replace(/on\w+\s*=/gi, '');
    };

    const sanitizeObject = (obj) => {
        for (let key in obj) {
            if (typeof obj[key] === 'string') {
                obj[key] = sanitizeString(obj[key]);
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                sanitizeObject(obj[key]);
            }
        }
    };

    if (req.body) sanitizeObject(req.body);
    if (req.query) sanitizeObject(req.query);
    if (req.params) sanitizeObject(req.params);

    next();
};
