import mongoose from "mongoose";

// Configure mongoose settings
mongoose.set('strictQuery', false);
mongoose.set('bufferCommands', false); // Disable mongoose buffering

// Set mongoose connection options for better reliability
const mongooseOptions = {
    maxPoolSize: 10, // Maintain up to 10 socket connections
    serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
    socketTimeoutMS: 45000, // Close sockets after 45 seconds
    family: 4, // Use IPv4, skip trying IPv6
};

const connectToDb = async () => {
    try {
        if (!process.env.MONGO_URI) {
            throw new Error('MONGO_URI environment variable is not defined');
        }

        const conn = await mongoose.connect(process.env.MONGO_URI, mongooseOptions);
        console.log(`✅ Database connected successfully: ${conn.connection.host}`);

        // Handle connection events
        mongoose.connection.on('error', (err) => {
            console.error('❌ Database connection error:', err);
        });

        mongoose.connection.on('disconnected', () => {
            console.warn('⚠️ Database disconnected');
        });

        mongoose.connection.on('reconnected', () => {
            console.log('✅ Database reconnected');
        });

        return conn;
    } catch (err) {
        console.error(`❌ Database connection failed: ${err.message}`);
        console.error('Stack:', err.stack);
        // Exit the process if database connection fails
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    try {
        await mongoose.connection.close();
        console.log('📦 Database connection closed through app termination');
    } catch (err) {
        console.error('Error closing database connection:', err);
    }
});

export default connectToDb;