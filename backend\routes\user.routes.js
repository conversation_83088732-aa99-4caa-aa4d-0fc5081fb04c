import { Router } from "express";

const router = Router();
import { register, login, logout, getProfile, forgotPassword, resetPassword, changePassword, updateUser,getAllUsers, updateUserByAdmin, verifyEmail, resendVerificationEmail, getUserById  } from '../controllers/user.controller.js';
import { isLoggedIn } from "../middleware/auth.middleware.js";
import upload from '../middleware/multer.middleware.js'
import { validateUserRegistration, validateUserLogin, validatePasswordChange, sanitizeInput } from '../middleware/validation.middleware.js';
import { authLimiter, uploadLimiter, passwordResetLimiter } from '../middleware/rateLimiter.middleware.js';

// Apply sanitization to all routes
router.use(sanitizeInput);

router.post('/register', authLimiter, upload.single("avatar"), uploadLimiter, validateUserRegistration, register);
router.post('/login', authLimiter, validateUserLogin, login);
router.get('/logout', logout);
router.get('/getuser',getAllUsers);
router.post('/updateuser/:userId', updateUserByAdmin);
router.post('/verify-email/:token', verifyEmail);
router.post('/resend-verification', authLimiter, resendVerificationEmail);
router.get('/getUserById/:id',getUserById);

router.get('/me', isLoggedIn, getProfile);
router.post('/reset', passwordResetLimiter, forgotPassword);
router.post('/reset/:resetToken', passwordResetLimiter, resetPassword);
router.post('/change-password', isLoggedIn, validatePasswordChange, changePassword);
router.post('/update/:id', isLoggedIn, upload.single("avatar"), uploadLimiter, updateUser);

export default router;