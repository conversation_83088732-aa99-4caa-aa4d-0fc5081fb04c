import { Router } from 'express';
import {
    createBlog,
    deleteBlog,
    getAllBlogs,
    getBlogById
} from '../controllers/blog.controller.js';
import upload from '../middleware/upload.middleware.js';
import { validateBlogCreation, sanitizeInput } from '../middleware/validation.middleware.js';
import { uploadLimiter } from '../middleware/rateLimiter.middleware.js';

const router = Router();

// Apply sanitization to all routes
router.use(sanitizeInput);

router
    .route('/create')
    .post(upload.single('thumbnail'), uploadLimiter, validateBlogCreation, createBlog);

router
    .route('/all')
    .get(getAllBlogs);

router
    .route('/:id')
    .get(getBlogById)
    .delete(deleteBlog);

export default router;