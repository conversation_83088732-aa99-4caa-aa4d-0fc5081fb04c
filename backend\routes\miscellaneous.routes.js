import { Router } from "express";
const router = Router();

import {contactUs, stats} from '../controllers/miscellaneous.controller.js';
import {isLoggedIn, authorisedRoles} from '../middleware/auth.middleware.js'
import { validateContactForm, sanitizeInput } from '../middleware/validation.middleware.js';
import { contactLimiter } from '../middleware/rateLimiter.middleware.js';

// Apply sanitization to all routes
router.use(sanitizeInput);

router.post("/contact", contactLimiter, validateContactForm, contactUs);
router.get("/admin/stats/users", isLoggedIn, authorisedRoles("ADMIN"), stats);

export default router;